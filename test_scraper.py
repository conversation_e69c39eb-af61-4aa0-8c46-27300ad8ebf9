#!/usr/bin/env python3
"""
Test script pro ov<PERSON><PERSON><PERSON><PERSON> funk<PERSON>ti scraperu
"""

import sys
import os
from scraper import BrigadaScraper
from config import Config

def test_scraper():
    """Test základní funkčnosti scraperu"""
    print("🧪 Testování Brigoška scraperu...")
    print(f"📡 URL: {Config.BRIGOSKA_URL}")
    print("-" * 50)
    
    # Vytvoření instance scraperu
    scraper = BrigadaScraper()
    
    # Test stahování dat
    print("📥 Stahování dat z webu...")
    brigady = scraper.fetch_brigady()
    
    if not brigady:
        print("❌ Nepodařilo se načíst žádné brigády!")
        return False
    
    print(f"✅ Načteno {len(brigady)} brigád")
    print()
    
    # Zobrazení prvních 3 brigád
    print("📋 První 3 brigády:")
    for i, brigada in enumerate(brigady[:3], 1):
        print(f"\n{i}. {brigada['nazev']}")
        print(f"   📅 {brigada['datum']} {brigada['cas_od']}-{brigada['cas_do']}")
        print(f"   💰 {brigada['mzda']}")
        print(f"   📦 Balení: {'Ano' if brigada['je_baleni'] else 'Ne'}")
        print(f"   💸 Ihned: {'Ano' if brigada['vyplata_ihned'] else 'Ne'}")
    
    print("\n" + "-" * 50)
    
    # Test filtrování
    print("🔍 Testování filtrů...")
    
    # Filtr na balení
    baleni_brigady = scraper.filter_brigady(brigady, jen_baleni=True)
    print(f"📦 Brigády na balení: {len(baleni_brigady)}")
    
    # Filtr na výplatu ihned
    ihned_brigady = scraper.filter_brigady(brigady, vyplata_ihned=True)
    print(f"💰 Brigády s výplatou ihned: {len(ihned_brigady)}")
    
    # Filtr na mzdu
    vysokomzdove = scraper.filter_brigady(brigady, min_mzda=180)
    print(f"💎 Brigády s mzdou 180+ Kč/h: {len(vysokomzdove)}")
    
    # Filtr na pohlaví
    zeny = scraper.filter_brigady(brigady, pohlavi="Ž")
    print(f"👩 Brigády pro ženy: {len(zeny)}")
    
    muzi = scraper.filter_brigady(brigady, pohlavi="M")
    print(f"👨 Brigády pro muže: {len(muzi)}")
    
    # Filtr na klíčové slovo
    kosmetika = scraper.filter_brigady(brigady, keyword="kosmetik")
    print(f"🧴 Brigády s 'kosmetik': {len(kosmetika)}")
    
    print("\n" + "=" * 50)
    print("✅ Test scraperu dokončen úspěšně!")
    
    return True

def test_config():
    """Test konfigurace"""
    print("⚙️ Testování konfigurace...")
    
    try:
        # Test načtení konfigurace
        print(f"🌐 URL: {Config.BRIGOSKA_URL}")
        print(f"🕐 Plánované hodiny: {Config.SCHEDULE_HOURS}")
        print(f"🌍 Timezone: {Config.TIMEZONE}")
        print(f"🤖 User Agent: {Config.USER_AGENT[:50]}...")
        
        # Test validace (bez Discord tokenu)
        if Config.DISCORD_TOKEN and Config.DISCORD_TOKEN != "your_discord_token_here":
            print("🔑 Discord token: ✅ Nastaven")
        else:
            print("🔑 Discord token: ⚠️ Není nastaven (potřebný pro spuštění bota)")
        
        if Config.GUILD_ID and Config.GUILD_ID != 0:
            print(f"🏠 Guild ID: ✅ {Config.GUILD_ID}")
        else:
            print("🏠 Guild ID: ⚠️ Není nastaven")
        
        if Config.CHANNEL_ID and Config.CHANNEL_ID != 0:
            print(f"📺 Channel ID: ✅ {Config.CHANNEL_ID}")
        else:
            print("📺 Channel ID: ⚠️ Není nastaven")
        
        print("✅ Konfigurace načtena úspěšně!")
        return True
        
    except Exception as e:
        print(f"❌ Chyba v konfiguraci: {e}")
        return False

def main():
    """Hlavní funkce testu"""
    print("🚀 Spouštění testů Brigoška Bot")
    print("=" * 50)
    
    # Test konfigurace
    config_ok = test_config()
    print()
    
    # Test scraperu
    scraper_ok = test_scraper()
    print()
    
    # Výsledek
    if config_ok and scraper_ok:
        print("🎉 Všechny testy prošly úspěšně!")
        print("💡 Bot je připraven k spuštění pomocí: python bot.py")
        return 0
    else:
        print("❌ Některé testy selhaly!")
        return 1

if __name__ == "__main__":
    exit(main())
