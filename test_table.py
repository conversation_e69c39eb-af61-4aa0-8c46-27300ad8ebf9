#!/usr/bin/env python3
"""
Test tabulkového zobrazení
"""

from scraper import BrigadaScraper
from bot import BrigadaBot

def test_table():
    """Test tabulkového zobrazení"""
    print("📊 Test tabulkového zobrazení...")
    
    # Vytvoření instance
    scraper = BrigadaScraper()
    bot = BrigadaBot()
    
    # Stáhnutí dat
    brigady = scraper.fetch_brigady()
    
    if not brigady:
        print("❌ Žádné brigády nenalezeny")
        return
    
    # Vytvoření tabulky
    table = bot.create_brigady_table(brigady[:5])  # Jen prvních 5 pro test
    
    print("📋 Ukázka tabulky:")
    print("-" * 80)
    print(table)
    print("-" * 80)
    
    print(f"✅ Tabulka vytvořena úspěšně! Délka: {len(table)} znaků")

if __name__ == "__main__":
    test_table()
