#!/bin/bash

# Spouštěcí script pro Brig<PERSON>š<PERSON> Discord Bot

echo "🚀 Spouštění B<PERSON> Discord Bot..."

# Kontrola virtual environment
if [ ! -d "venv" ]; then
    echo "❌ Virtual environment nenalezen!"
    echo "💡 Spusťte: python3 -m venv venv"
    exit 1
fi

# Aktivace virtual environment
echo "📦 Aktivace virtual environment..."
source venv/bin/activate

# Kontrola závislostí
echo "🔍 Kontrola závislostí..."
pip install -r requirements.txt --quiet

# Kontrola konfigurace
echo "⚙️ Kontrola konfigurace..."
if [ ! -f ".env" ]; then
    echo "❌ .env soubor nenalezen!"
    echo "💡 Zkopírujte .env.example do .env a vyplňte Discord token"
    exit 1
fi

# Test scraperu
echo "🧪 Test scraperu..."
python test_scraper.py --quiet

if [ $? -ne 0 ]; then
    echo "❌ Test scraperu selhal!"
    exit 1
fi

echo "✅ Všechny kontroly prošly!"
echo "🤖 Spouštění Discord bota..."
echo "📝 Pro zastavení použijte Ctrl+C"
echo "=" * 50

# Spuštění bota
python bot.py
