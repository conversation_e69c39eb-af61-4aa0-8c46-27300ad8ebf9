#!/usr/bin/env fish

# Spouštěcí script pro <PERSON>rigo<PERSON><PERSON> Discord <PERSON> (Fish shell)

echo "🚀 Spouštění Brig<PERSON> Discord Bot..."

# Kontrola virtual environment
if not test -d "venv"
    echo "❌ Virtual environment nenalezen!"
    echo "💡 Spusťte: python3 -m venv venv"
    exit 1
end

# Aktivace virtual environment
echo "📦 Aktivace virtual environment..."
source venv/bin/activate.fish

# Kontrola závislostí
echo "🔍 Kontrola závislostí..."
pip install -r requirements.txt --quiet

# Kontrola konfigurace
echo "⚙️ Kontrola konfigurace..."
if not test -f ".env"
    echo "❌ .env soubor nenalezen!"
    echo "💡 Zkopírujte .env do .env.local a vyplňte Discord token"
    exit 1
end

# Test scraperu
echo "🧪 Test scraperu..."
python test_scraper.py

if test $status -ne 0
    echo "❌ Test scraperu selhal!"
    exit 1
end

echo "✅ Všechny kontroly prošly!"
echo "🤖 Spouštění Discord bota..."
echo "📝 Pro zastavení použijte Ctrl+C"
echo "=================================================="

# Spuštění bota
python bot.py
