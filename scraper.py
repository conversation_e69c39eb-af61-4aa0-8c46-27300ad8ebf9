import requests
from bs4 import BeautifulSoup
import re
from datetime import datetime
from typing import List, Dict, Optional
from config import Config
import logging

# Nastavení loggingu
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BrigadaScraper:
    """Scraper pro stahování brigád z brigoska.cz"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': Config.USER_AGENT,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'cs,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        })
    
    def fetch_brigady(self) -> List[Dict]:
        """Stáhne a parsuje brigády z webu"""
        try:
            logger.info(f"Stahování dat z {Config.BRIGOSKA_URL}")
            response = self.session.get(Config.BRIGOSKA_URL, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            brigady = self._parse_brigady(soup)
            
            logger.info(f"Nalezeno {len(brigady)} brigád")
            return brigady
            
        except Exception as e:
            logger.error(f"Chyba při stahování dat: {e}")
            return []
    
    def _parse_brigady(self, soup: BeautifulSoup) -> List[Dict]:
        """Parsuje brigády z HTML"""
        brigady = []
        
        # Najdeme tabulku s brigádami
        table = soup.find('table', {'id': 'brigady'})
        if not table:
            logger.warning("Tabulka s brigádami nebyla nalezena")
            return brigady
        
        # Parsujeme řádky tabulky
        rows = table.find('tbody').find_all('tr') if table.find('tbody') else []
        
        for row in rows:
            try:
                brigada = self._parse_row(row)
                if brigada:
                    brigady.append(brigada)
            except Exception as e:
                logger.warning(f"Chyba při parsování řádku: {e}")
                continue
        
        return brigady
    
    def _parse_row(self, row) -> Optional[Dict]:
        """Parsuje jeden řádek tabulky"""
        cells = row.find_all('td')
        if len(cells) < 6:
            return None
        
        # Extrakce dat z buněk
        nazev_cell = cells[1]
        cas_cell = cells[2]
        vek_cell = cells[3]
        poznamka_cell = cells[4]
        pohlavi_cell = cells[5]
        mzda_cell = cells[6] if len(cells) > 6 else None
        
        # Název brigády
        nazev = nazev_cell.get_text(strip=True)
        
        # Čas a datum
        cas_text = cas_cell.get_text(strip=True)
        datum, cas_od, cas_do, delka = self._parse_datetime(cas_text)
        
        # Věkové omezení
        vek = vek_cell.get_text(strip=True) if vek_cell else ""
        
        # Poznámka (např. RT)
        poznamka = poznamka_cell.get_text(strip=True) if poznamka_cell else ""
        
        # Pohlaví
        pohlavi = pohlavi_cell.get_text(strip=True) if pohlavi_cell else ""
        
        # Mzda
        mzda = mzda_cell.get_text(strip=True) if mzda_cell else ""
        mzda_cislo = self._extract_mzda(mzda)
        
        # Kontrola, zda se jedná o balení elektroniky/kosmetiky
        je_baleni = self._je_baleni_elektroniky(nazev)
        
        # Kontrola, zda má výplatu ihned
        vyplata_ihned = "ihned" in nazev.lower() or "ihned" in poznamka.lower()
        
        return {
            'nazev': nazev,
            'datum': datum,
            'cas_od': cas_od,
            'cas_do': cas_do,
            'delka': delka,
            'vek': vek,
            'poznamka': poznamka,
            'pohlavi': pohlavi,
            'mzda': mzda,
            'mzda_cislo': mzda_cislo,
            'je_baleni': je_baleni,
            'vyplata_ihned': vyplata_ihned,
            'raw_cas': cas_text
        }
    
    def _parse_datetime(self, cas_text: str) -> tuple:
        """Parsuje datum a čas z textu"""
        try:
            # Příklad: "26.6.2025 Čt 22:00 - 06:00 (8h)"
            pattern = r'(\d{1,2}\.\d{1,2}\.\d{4})\s+\w+\s+(\d{1,2}:\d{2})\s*-\s*(\d{1,2}:\d{2})\s*\(([^)]+)\)'
            match = re.search(pattern, cas_text)
            
            if match:
                datum = match.group(1)
                cas_od = match.group(2)
                cas_do = match.group(3)
                delka = match.group(4)
                return datum, cas_od, cas_do, delka
            
            return "", "", "", ""
        except Exception:
            return "", "", "", ""
    
    def _extract_mzda(self, mzda_text: str) -> int:
        """Extrahuje číselnou hodnotu mzdy"""
        try:
            # Hledáme číslo před "Kč/h"
            match = re.search(r'(\d+)\s*Kč/h', mzda_text)
            if match:
                return int(match.group(1))
            return 0
        except Exception:
            return 0
    
    def _je_baleni_elektroniky(self, nazev: str) -> bool:
        """Kontroluje, zda se jedná o balení elektroniky/kosmetiky"""
        klicova_slova = [
            'balení', 'kosmetick', 'elektronik', 'výrobk', 
            'balení kosmetických výrobků', 'balení elektroniky'
        ]
        nazev_lower = nazev.lower()
        return any(slovo in nazev_lower for slovo in klicova_slova)
    
    def filter_brigady(self, brigady: List[Dict], **filters) -> List[Dict]:
        """Filtruje brigády podle zadaných kritérií"""
        filtered = brigady.copy()
        
        # Filtr podle typu práce
        if filters.get('jen_baleni'):
            filtered = [b for b in filtered if b['je_baleni']]
        
        # Filtr podle výplaty ihned
        if filters.get('vyplata_ihned'):
            filtered = [b for b in filtered if b['vyplata_ihned']]
        
        # Filtr podle minimální mzdy
        if filters.get('min_mzda'):
            min_mzda = int(filters['min_mzda'])
            filtered = [b for b in filtered if b['mzda_cislo'] >= min_mzda]
        
        # Filtr podle pohlaví
        if filters.get('pohlavi'):
            pohlavi = filters['pohlavi'].upper()
            filtered = [b for b in filtered if pohlavi in b['pohlavi']]
        
        # Filtr podle klíčového slova v názvu
        if filters.get('keyword'):
            keyword = filters['keyword'].lower()
            filtered = [b for b in filtered if keyword in b['nazev'].lower()]
        
        return filtered
