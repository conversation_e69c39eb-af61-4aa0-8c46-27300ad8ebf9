import discord
from discord.ext import commands, tasks
import asyncio
import logging
from datetime import datetime, time
import pytz
from typing import List, Dict
from config import Config
from scraper import BrigadaScraper

# Interaktivní komponenty
class BrigadyView(discord.ui.View):
    """View s tlačítky pro filtrování brigád"""

    def __init__(self, bot_instance):
        super().__init__(timeout=300)  # 5 minut timeout
        self.bot = bot_instance

    @discord.ui.button(label="📦 Jen balení", style=discord.ButtonStyle.primary)
    async def baleni_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        await interaction.response.defer()
        try:
            brigady = self.bot.scraper.fetch_brigady()
            baleni_brigady = self.bot.scraper.filter_brigady(brigady, jen_baleni=True)
            embed = self.bot.create_brigady_embed(baleni_brigady, "📦 Brigá<PERSON> na balení")
            await interaction.followup.send(embed=embed, ephemeral=True)
        except Exception as e:
            await interaction.followup.send("❌ Chyba při načítání brigád.", ephemeral=True)

    @discord.ui.button(label="💰 Výplata ihned", style=discord.ButtonStyle.success)
    async def ihned_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        await interaction.response.defer()
        try:
            brigady = self.bot.scraper.fetch_brigady()
            ihned_brigady = self.bot.scraper.filter_brigady(brigady, vyplata_ihned=True)
            embed = self.bot.create_brigady_embed(ihned_brigady, "💰 Brigády s výplatou IHNED")
            await interaction.followup.send(embed=embed, ephemeral=True)
        except Exception as e:
            await interaction.followup.send("❌ Chyba při načítání brigád.", ephemeral=True)

    @discord.ui.button(label="🔄 Obnovit", style=discord.ButtonStyle.secondary)
    async def refresh_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        await interaction.response.defer()
        try:
            brigady = self.bot.scraper.fetch_brigady()
            embed = self.bot.create_brigady_embed(brigady, "🔄 Aktualizované brigády")
            await interaction.followup.send(embed=embed, ephemeral=True)
        except Exception as e:
            await interaction.followup.send("❌ Chyba při načítání brigád.", ephemeral=True)

class MzdaSelect(discord.ui.Select):
    """Select menu pro výběr minimální mzdy"""

    def __init__(self, bot_instance):
        self.bot = bot_instance
        options = [
            discord.SelectOption(label="Všechny mzdy", value="0", description="Bez omezení mzdy"),
            discord.SelectOption(label="Min. 160 Kč/h", value="160", description="Minimálně 160 Kč za hodinu"),
            discord.SelectOption(label="Min. 170 Kč/h", value="170", description="Minimálně 170 Kč za hodinu"),
            discord.SelectOption(label="Min. 180 Kč/h", value="180", description="Minimálně 180 Kč za hodinu"),
            discord.SelectOption(label="Min. 190 Kč/h", value="190", description="Minimálně 190 Kč za hodinu"),
        ]
        super().__init__(placeholder="Vyberte minimální mzdu...", options=options)

    async def callback(self, interaction: discord.Interaction):
        await interaction.response.defer()
        try:
            min_mzda = int(self.values[0])
            brigady = self.bot.scraper.fetch_brigady()

            if min_mzda > 0:
                filtered_brigady = self.bot.scraper.filter_brigady(brigady, min_mzda=min_mzda)
                title = f"💰 Brigády s mzdou min. {min_mzda} Kč/h"
            else:
                filtered_brigady = brigady
                title = "💰 Všechny brigády"

            embed = self.bot.create_brigady_embed(filtered_brigady, title)
            await interaction.followup.send(embed=embed, ephemeral=True)
        except Exception as e:
            await interaction.followup.send("❌ Chyba při filtrování brigád.", ephemeral=True)

class MzdaView(discord.ui.View):
    """View s select menu pro mzdu"""

    def __init__(self, bot_instance):
        super().__init__(timeout=300)
        self.add_item(MzdaSelect(bot_instance))

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BrigadaBot(commands.Bot):
    """Discord bot pro brigády z Brigoška"""
    
    def __init__(self):
        intents = discord.Intents.default()
        intents.message_content = True
        
        super().__init__(
            command_prefix='!',
            intents=intents,
            description='Bot pro sledování brigád z Brigoška'
        )
        
        self.scraper = BrigadaScraper()
        self.timezone = pytz.timezone(Config.TIMEZONE)
        
    async def setup_hook(self):
        """Nastavení při spuštění bota"""
        logger.info("Bot se připravuje...")
        
        # Synchronizace slash commandů
        try:
            synced = await self.tree.sync()
            logger.info(f"Synchronizováno {len(synced)} slash commandů")
        except Exception as e:
            logger.error(f"Chyba při synchronizaci commandů: {e}")
        
        # Spuštění scheduleru
        self.scheduler_task.start()
        logger.info("Scheduler spuštěn")
    
    async def on_ready(self):
        """Událost při připojení bota"""
        logger.info(f'{self.user} se připojil k Discordu!')
        logger.info(f'Bot je připojen k {len(self.guilds)} serverům')
        
        # Nastavení statusu
        await self.change_presence(
            activity=discord.Activity(
                type=discord.ActivityType.watching,
                name="brigády na Brigoška.cz"
            )
        )
    
    @tasks.loop(minutes=1)
    async def scheduler_task(self):
        """Kontroluje čas a posílá zprávy v naplánovaných hodinách"""
        now = datetime.now(self.timezone)
        
        # Kontrola, zda je čas poslat zprávu
        if (now.hour in Config.SCHEDULE_HOURS and 
            now.minute == 0 and 
            now.second < 30):  # Malé okno pro spuštění
            
            logger.info(f"Posílání naplánované zprávy v {now.hour}:00")
            await self.send_scheduled_message()
    
    async def send_scheduled_message(self):
        """Pošle naplánovanou zprávu s brigádami"""
        try:
            channel = self.get_channel(Config.CHANNEL_ID)
            if not channel:
                logger.error(f"Kanál {Config.CHANNEL_ID} nebyl nalezen")
                return
            
            # Stáhnutí brigád
            brigady = self.scraper.fetch_brigady()
            if not brigady:
                await channel.send("❌ Nepodařilo se načíst brigády z webu.")
                return
            
            # Filtrování brigád zaměřených na balení
            baleni_brigady = self.scraper.filter_brigady(brigady, jen_baleni=True)
            
            # Vytvoření embed zprávy
            embed = self.create_brigady_embed(baleni_brigady, "🔔 Automatická aktualizace brigád")
            
            await channel.send(embed=embed)
            logger.info(f"Naplánovaná zpráva odeslána do kanálu {channel.name}")
            
        except Exception as e:
            logger.error(f"Chyba při posílání naplánované zprávy: {e}")
    
    def create_brigady_embed(self, brigady: List[Dict], title: str = "Brigády z Brigoška") -> discord.Embed:
        """Vytvoří Discord embed s brigádami"""
        embed = discord.Embed(
            title=title,
            color=0x00ff00,
            timestamp=datetime.now(),
            url=Config.BRIGOSKA_URL
        )
        
        if not brigady:
            embed.description = "Žádné brigády nebyly nalezeny."
            embed.color = 0xff0000
            return embed
        
        # Seřazení podle data a času
        brigady_sorted = sorted(brigady, key=lambda x: (x['datum'], x['cas_od']))
        
        # Omezení na prvních 10 brigád (kvůli limitu Discord embeds)
        brigady_to_show = brigady_sorted[:10]
        
        embed.description = f"Nalezeno **{len(brigady)}** brigád"
        if len(brigady) > 10:
            embed.description += f" (zobrazeno prvních 10)"
        
        for i, brigada in enumerate(brigady_to_show, 1):
            # Ikony podle typu
            icon = "💼"
            if brigada['je_baleni']:
                icon = "📦"
            if brigada['vyplata_ihned']:
                icon = "💰"
            
            # Název pole
            field_name = f"{icon} {brigada['nazev'][:50]}{'...' if len(brigada['nazev']) > 50 else ''}"
            
            # Obsah pole
            field_value = f"**📅 Datum:** {brigada['datum']}\n"
            field_value += f"**🕐 Čas:** {brigada['cas_od']} - {brigada['cas_do']} ({brigada['delka']})\n"
            field_value += f"**💰 Mzda:** {brigada['mzda']}\n"
            
            if brigada['vek']:
                field_value += f"**👤 Věk:** {brigada['vek']}\n"
            if brigada['pohlavi']:
                field_value += f"**⚧ Pohlaví:** {brigada['pohlavi']}\n"
            if brigada['poznamka']:
                field_value += f"**📝 Poznámka:** {brigada['poznamka']}\n"
            
            embed.add_field(
                name=field_name,
                value=field_value,
                inline=True
            )
        
        # Footer s informacemi
        embed.set_footer(
            text="📞 Rezervace: 541262626 | 📱 SMS: 774069679",
            icon_url="https://www.brigoska.cz/templates/brigoska/images/logo.png"
        )
        
        return embed

# Vytvoření instance bota
bot = BrigadaBot()

# Slash commandy
@bot.tree.command(name="brigady", description="Zobrazí aktuální brigády")
async def brigady_command(interaction: discord.Interaction):
    """Slash command pro zobrazení brigád"""
    await interaction.response.defer()
    
    try:
        brigady = bot.scraper.fetch_brigady()
        embed = bot.create_brigady_embed(brigady, "🔍 Aktuální brigády")
        await interaction.followup.send(embed=embed)
        
    except Exception as e:
        logger.error(f"Chyba v brigady_command: {e}")
        await interaction.followup.send("❌ Nastala chyba při načítání brigád.")

@bot.tree.command(name="baleni", description="Zobrazí pouze brigády na balení")
async def baleni_command(interaction: discord.Interaction):
    """Slash command pro brigády na balení"""
    await interaction.response.defer()

    try:
        brigady = bot.scraper.fetch_brigady()
        baleni_brigady = bot.scraper.filter_brigady(brigady, jen_baleni=True)
        embed = bot.create_brigady_embed(baleni_brigady, "📦 Brigády na balení")
        await interaction.followup.send(embed=embed)

    except Exception as e:
        logger.error(f"Chyba v baleni_command: {e}")
        await interaction.followup.send("❌ Nastala chyba při načítání brigád.")

@bot.tree.command(name="ihned", description="Zobrazí brigády s výplatou ihned")
async def ihned_command(interaction: discord.Interaction):
    """Slash command pro brigády s výplatou ihned"""
    await interaction.response.defer()

    try:
        brigady = bot.scraper.fetch_brigady()
        ihned_brigady = bot.scraper.filter_brigady(brigady, vyplata_ihned=True)
        embed = bot.create_brigady_embed(ihned_brigady, "💰 Brigády s výplatou IHNED")
        await interaction.followup.send(embed=embed)

    except Exception as e:
        logger.error(f"Chyba v ihned_command: {e}")
        await interaction.followup.send("❌ Nastala chyba při načítání brigád.")

@bot.tree.command(name="hledat", description="Vyhledá brigády podle klíčového slova")
async def hledat_command(interaction: discord.Interaction, keyword: str):
    """Slash command pro vyhledávání brigád"""
    await interaction.response.defer()

    try:
        brigady = bot.scraper.fetch_brigady()
        filtered_brigady = bot.scraper.filter_brigady(brigady, keyword=keyword)
        embed = bot.create_brigady_embed(filtered_brigady, f"🔍 Vyhledávání: '{keyword}'")
        await interaction.followup.send(embed=embed)

    except Exception as e:
        logger.error(f"Chyba v hledat_command: {e}")
        await interaction.followup.send("❌ Nastala chyba při vyhledávání brigád.")

@bot.tree.command(name="mzda", description="Zobrazí brigády s minimální mzdou")
async def mzda_command(interaction: discord.Interaction, min_mzda: int):
    """Slash command pro filtrování podle mzdy"""
    await interaction.response.defer()

    try:
        brigady = bot.scraper.fetch_brigady()
        filtered_brigady = bot.scraper.filter_brigady(brigady, min_mzda=min_mzda)
        embed = bot.create_brigady_embed(filtered_brigady, f"💰 Brigády s mzdou min. {min_mzda} Kč/h")
        await interaction.followup.send(embed=embed)

    except Exception as e:
        logger.error(f"Chyba v mzda_command: {e}")
        await interaction.followup.send("❌ Nastala chyba při filtrování brigád.")

@bot.tree.command(name="pohlavi", description="Zobrazí brigády podle pohlaví")
async def pohlavi_command(interaction: discord.Interaction, pohlavi: str):
    """Slash command pro filtrování podle pohlaví"""
    await interaction.response.defer()

    try:
        brigady = bot.scraper.fetch_brigady()
        filtered_brigady = bot.scraper.filter_brigady(brigady, pohlavi=pohlavi)
        embed = bot.create_brigady_embed(filtered_brigady, f"⚧ Brigády pro pohlaví: {pohlavi}")
        await interaction.followup.send(embed=embed)

    except Exception as e:
        logger.error(f"Chyba v pohlavi_command: {e}")
        await interaction.followup.send("❌ Nastala chyba při filtrování brigád.")

@bot.tree.command(name="interaktivni", description="Interaktivní prohlížení brigád s tlačítky")
async def interaktivni_command(interaction: discord.Interaction):
    """Slash command s interaktivními tlačítky"""
    await interaction.response.defer()

    try:
        brigady = bot.scraper.fetch_brigady()
        embed = bot.create_brigady_embed(brigady, "🎮 Interaktivní prohlížení brigád")
        embed.add_field(
            name="💡 Tip",
            value="Použijte tlačítka níže pro filtrování brigád podle vašich preferencí!",
            inline=False
        )

        view = BrigadyView(bot)
        await interaction.followup.send(embed=embed, view=view)

    except Exception as e:
        logger.error(f"Chyba v interaktivni_command: {e}")
        await interaction.followup.send("❌ Nastala chyba při načítání brigád.")

@bot.tree.command(name="mzda_select", description="Výběr brigád podle mzdy pomocí menu")
async def mzda_select_command(interaction: discord.Interaction):
    """Slash command s select menu pro mzdu"""
    await interaction.response.defer()

    try:
        embed = discord.Embed(
            title="💰 Filtrování podle mzdy",
            description="Vyberte minimální mzdu z menu níže:",
            color=0x00ff00
        )

        view = MzdaView(bot)
        await interaction.followup.send(embed=embed, view=view)

    except Exception as e:
        logger.error(f"Chyba v mzda_select_command: {e}")
        await interaction.followup.send("❌ Nastala chyba při vytváření menu.")

@bot.tree.command(name="help", description="Zobrazí nápovědu k botovi")
async def help_command(interaction: discord.Interaction):
    """Slash command pro nápovědu"""
    embed = discord.Embed(
        title="🤖 Nápověda - Brigoška Bot",
        description="Bot pro sledování brigád z Brigoška.cz",
        color=0x0099ff
    )

    embed.add_field(
        name="📋 Základní příkazy",
        value="`/brigady` - Zobrazí všechny aktuální brigády\n"
              "`/baleni` - Pouze brigády na balení\n"
              "`/ihned` - Brigády s výplatou ihned\n"
              "`/help` - Tato nápověda",
        inline=False
    )

    embed.add_field(
        name="🔍 Vyhledávání",
        value="`/hledat <slovo>` - Vyhledá brigády podle klíčového slova\n"
              "`/mzda <částka>` - Brigády s minimální mzdou\n"
              "`/pohlavi <M/Ž>` - Brigády podle pohlaví",
        inline=False
    )

    embed.add_field(
        name="🎮 Interaktivní",
        value="`/interaktivni` - Prohlížení s tlačítky\n"
              "`/mzda_select` - Výběr mzdy pomocí menu",
        inline=False
    )

    embed.add_field(
        name="⏰ Automatické zprávy",
        value="Bot automaticky posílá aktualizace v 11:00, 12:00 a 13:00",
        inline=False
    )

    embed.add_field(
        name="📞 Kontakt na Brigoška",
        value="**Telefon:** 541262626\n**SMS:** 774069679",
        inline=False
    )

    embed.set_footer(text="Vytvořeno pro sledování brigád z brigoska.cz")

    await interaction.response.send_message(embed=embed)

def main():
    """Hlavní funkce pro spuštění bota"""
    try:
        # Validace konfigurace
        Config.validate()
        
        # Spuštění bota
        logger.info("Spouštění Discord bota...")
        bot.run(Config.DISCORD_TOKEN)
        
    except Exception as e:
        logger.error(f"Chyba při spuštění bota: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
