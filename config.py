import os
from dotenv import load_dotenv

# Načtení .env souboru
load_dotenv()

class Config:
    """Konfigurace pro Discord bota"""
    
    # Discord nastavení
    DISCORD_TOKEN = os.getenv('DISCORD_TOKEN')
    GUILD_ID = int(os.getenv('GUILD_ID', 0))
    CHANNEL_ID = int(os.getenv('CHANNEL_ID', 0))
    
    # URL pro scraping
    BRIGOSKA_URL = os.getenv('BRIGOSKA_URL', 'https://www.brigoska.cz/cs/mista')
    
    # Časové nastavení
    SCHEDULE_HOURS = [int(h.strip()) for h in os.getenv('SCHEDULE_HOURS', '11,12,13').split(',')]
    
    # Timezone
    TIMEZONE = os.getenv('TIMEZONE', 'Europe/Prague')
    
    # User Agent pro web scraping
    USER_AGENT = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    
    @classmethod
    def validate(cls):
        """Validace konfigurace"""
        if not cls.DISCORD_TOKEN:
            raise ValueError("DISCORD_TOKEN není nastaven v .env souboru")
        if not cls.GUILD_ID:
            raise ValueError("GUILD_ID není nastaven v .env souboru")
        if not cls.CHANNEL_ID:
            raise ValueError("CHANNEL_ID není nastaven v .env souboru")
        return True
