# 🤖 <PERSON>rigoš<PERSON> Discord Bot

Discord bot pro automatické sledování a vyhledávání brigád z [Brigoška.cz](https://www.brigoska.cz/cs/mista).

## ✨ Funkce

- 🔔 **Automatick<PERSON> zpr<PERSON>** - Posílá aktualizace brigád každý den v 11:00, 12:00 a 13:00
- 📦 **Zaměření na balení** - Speciálně optimalizováno pro brigády na balení elektroniky/kosmetiky
- 🔍 **Pokročilé vyhledávání** - Filtrování podle mzdy, pohlaví, kl<PERSON><PERSON>ov<PERSON>ch slov
- 🎮 **Interaktivní rozhraní** - Tlačítka a select menu pro snadné ovládání
- 💰 **Výplata ihned** - Zvýrazňuje brigády s okamžitou výplatou

## 🚀 Instalace

### 1. Klonování repozitáře
```bash
git clone <repository-url>
cd cinkacka-bot
```

### 2. Vytvoření virtual environment (Fish shell)
```fish
python3 -m venv venv
source venv/bin/activate.fish
```

### 3. Instalace závislostí
```fish
pip install -r requirements.txt
```

### 4. Konfigurace

Zkopírujte `.env` soubor a vyplňte své údaje:

```bash
cp .env .env.local
```

Upravte `.env` soubor:
```env
# Discord Bot Token - získej z https://discord.com/developers/applications
DISCORD_TOKEN=your_discord_token_here

# Discord Server a Channel IDs
GUILD_ID=1033782584934662144
CHANNEL_ID=1387877624423907348

# URL pro scraping
BRIGOSKA_URL=https://www.brigoska.cz/cs/mista

# Časové nastavení (hodiny v 24h formátu)
SCHEDULE_HOURS=11,12,13

# Timezone
TIMEZONE=Europe/Prague
```

### 5. Vytvoření Discord aplikace

1. Jděte na [Discord Developer Portal](https://discord.com/developers/applications)
2. Vytvořte novou aplikaci
3. V sekci "Bot" vytvořte bota a zkopírujte token
4. V sekci "OAuth2 > URL Generator" vyberte:
   - Scopes: `bot`, `applications.commands`
   - Bot Permissions: `Send Messages`, `Use Slash Commands`, `Embed Links`
5. Použijte vygenerovanou URL pro přidání bota na server

## 🎮 Použití

### Spuštění bota
```fish
source venv/bin/activate.fish
python bot.py
```

### Slash příkazy

#### 📋 Základní příkazy
- `/brigady` - Zobrazí všechny aktuální brigády
- `/baleni` - Pouze brigády na balení elektroniky/kosmetiky
- `/ihned` - Brigády s výplatou ihned
- `/help` - Nápověda

#### 🔍 Vyhledávání
- `/hledat <slovo>` - Vyhledá brigády podle klíčového slova
- `/mzda <částka>` - Brigády s minimální mzdou (např. `/mzda 180`)
- `/pohlavi <M/Ž>` - Brigády podle pohlaví

#### 🎮 Interaktivní
- `/interaktivni` - Prohlížení s tlačítky pro rychlé filtrování
- `/mzda_select` - Výběr minimální mzdy pomocí dropdown menu

### Automatické zprávy

Bot automaticky posílá aktualizace brigád:
- **11:00** - Ranní přehled
- **12:00** - Polední aktualizace  
- **13:00** - Odpolední přehled

Zprávy se posílají do nastaveného kanálu a zaměřují se především na brigády balení.

## 📁 Struktura projektu

```
cinkacka-bot/
├── bot.py              # Hlavní soubor s Discord botem
├── scraper.py          # Web scraping funkcionalita
├── config.py           # Konfigurace a nastavení
├── .env               # Konfigurační soubor (neverzovaný)
├── requirements.txt    # Python závislosti
├── README.md          # Dokumentace
└── venv/              # Virtual environment
```

## 🛠️ Technologie

- **Python 3.11+**
- **discord.py** - Discord API wrapper
- **BeautifulSoup4** - HTML parsing
- **requests** - HTTP požadavky
- **pytz** - Timezone handling
- **python-dotenv** - Environment variables

## 🔧 Konfigurace

### Časové pásmo
Bot používá timezone `Europe/Prague`. Můžete změnit v `.env`:
```env
TIMEZONE=Europe/Prague
```

### Plánované časy
Upravte časy automatických zpráv v `.env`:
```env
SCHEDULE_HOURS=11,12,13,15,17
```

### Filtrování
Bot automaticky zvýrazňuje:
- 📦 Brigády na balení (obsahují klíčová slova: balení, kosmetick, elektronik)
- 💰 Brigády s výplatou ihned
- 🔥 Vysoké mzdy (180+ Kč/h)

## 📞 Kontakt na Brigoška

- **Telefon:** 541262626
- **SMS:** 774069679
- **Web:** [brigoska.cz](https://www.brigoska.cz)

## 📝 Licence

Tento projekt je vytvořen pro osobní použití. Respektujte podmínky použití webu Brigoška.cz.

## 🐛 Řešení problémů

### Bot se nespustí
1. Zkontrolujte, že máte správný Discord token v `.env`
2. Ověřte, že bot má potřebná oprávnění na serveru
3. Zkontrolujte, že jsou správně nastavené ID serveru a kanálu

### Nefunguje scraping
1. Zkontrolujte internetové připojení
2. Ověřte, že web Brigoška.cz je dostupný
3. Možná se změnila struktura webu - kontaktujte vývojáře

### Automatické zprávy se neposílají
1. Zkontrolujte timezone nastavení
2. Ověřte, že bot běží nepřetržitě
3. Zkontrolujte logy pro chybové zprávy

## 🔄 Aktualizace

Pro aktualizaci bota:
```fish
git pull
source venv/bin/activate.fish
pip install -r requirements.txt --upgrade
python bot.py
```
